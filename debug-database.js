// Debug script to check database state
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugDatabase() {
  console.log('🔍 Debugging TalentSol Database...\n');

  try {
    // Check companies
    console.log('📊 COMPANIES:');
    const companies = await prisma.company.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            jobs: true,
            users: true
          }
        }
      }
    });
    
    companies.forEach(company => {
      console.log(`  - ID: ${company.id} | Name: ${company.name} | Jobs: ${company._count.jobs} | Users: ${company._count.users}`);
    });

    // Check jobs
    console.log('\n📋 JOBS:');
    const jobs = await prisma.job.findMany({
      select: {
        id: true,
        title: true,
        companyId: true,
        _count: {
          select: {
            applications: true
          }
        }
      },
      take: 10
    });
    
    jobs.forEach(job => {
      console.log(`  - ID: ${job.id} | Title: ${job.title} | Company: ${job.companyId} | Applications: ${job._count.applications}`);
    });

    // Check applications
    console.log('\n📝 APPLICATIONS:');
    const applications = await prisma.application.findMany({
      select: {
        id: true,
        status: true,
        submittedAt: true,
        job: {
          select: {
            title: true,
            companyId: true
          }
        },
        candidate: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      },
      take: 10
    });
    
    applications.forEach(app => {
      console.log(`  - ID: ${app.id} | Status: ${app.status} | Job: ${app.job.title} | Company: ${app.job.companyId} | Candidate: ${app.candidate.firstName} ${app.candidate.lastName}`);
    });

    // Check applications by company
    console.log('\n📊 APPLICATIONS BY COMPANY:');
    for (const company of companies) {
      const appCount = await prisma.application.count({
        where: {
          job: {
            companyId: company.id
          }
        }
      });
      console.log(`  - Company ${company.id} (${company.name}): ${appCount} applications`);
    }

    // Test the admin user scenario
    console.log('\n🔧 TESTING ADMIN USER SCENARIO:');
    const firstCompany = await prisma.company.findFirst();
    if (firstCompany) {
      console.log(`Admin user would be assigned to company: ${firstCompany.id} (${firstCompany.name})`);
      
      const adminCompanyApps = await prisma.application.count({
        where: {
          job: {
            companyId: firstCompany.id
          }
        }
      });
      console.log(`Applications for admin company: ${adminCompanyApps}`);
      
      if (adminCompanyApps === 0) {
        console.log('⚠️ ISSUE FOUND: Admin company has no applications!');
        
        // Check if there are applications in other companies
        const totalApps = await prisma.application.count();
        console.log(`Total applications in database: ${totalApps}`);
        
        if (totalApps > 0) {
          console.log('💡 SOLUTION: Applications exist but in different companies');
          
          // Find company with most applications
          const companiesWithApps = await Promise.all(
            companies.map(async (company) => {
              const count = await prisma.application.count({
                where: { job: { companyId: company.id } }
              });
              return { ...company, applicationCount: count };
            })
          );
          
          const bestCompany = companiesWithApps.reduce((max, company) => 
            company.applicationCount > max.applicationCount ? company : max
          );
          
          console.log(`Company with most applications: ${bestCompany.id} (${bestCompany.name}) - ${bestCompany.applicationCount} apps`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugDatabase();
