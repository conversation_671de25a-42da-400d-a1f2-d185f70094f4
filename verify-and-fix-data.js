// Verify and fix data structure for TalentSol ATS
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyAndFixData() {
  console.log('🔧 Verifying and Fixing TalentSol ATS Data Structure...\n');

  try {
    // 1. Ensure company 'comp_1' exists
    console.log('1️⃣ Checking company comp_1...');
    
    let company = await prisma.company.findUnique({
      where: { id: 'comp_1' }
    });
    
    if (!company) {
      console.log('   Creating company comp_1...');
      company = await prisma.company.create({
        data: {
          id: 'comp_1',
          name: 'TalentSol Demo Company',
          domain: 'talentsol.com'
        }
      });
      console.log('   ✅ Created company:', company.name);
    } else {
      console.log('   ✅ Company exists:', company.name);
    }

    // 2. Check and fix job assignments
    console.log('\n2️⃣ Checking job assignments...');
    
    const totalJobs = await prisma.job.count();
    const comp1Jobs = await prisma.job.count({
      where: { companyId: 'comp_1' }
    });
    
    console.log(`   Total jobs in database: ${totalJobs}`);
    console.log(`   Jobs assigned to comp_1: ${comp1Jobs}`);
    
    if (totalJobs > 0 && comp1Jobs === 0) {
      console.log('   🔧 Fixing job assignments - assigning all jobs to comp_1...');
      
      const updateResult = await prisma.job.updateMany({
        data: { companyId: 'comp_1' }
      });
      
      console.log(`   ✅ Updated ${updateResult.count} jobs to belong to comp_1`);
    }

    // 3. Verify applications
    console.log('\n3️⃣ Checking applications...');
    
    const totalApplications = await prisma.application.count();
    const comp1Applications = await prisma.application.count({
      where: {
        job: { companyId: 'comp_1' }
      }
    });
    
    console.log(`   Total applications: ${totalApplications}`);
    console.log(`   Applications for comp_1 jobs: ${comp1Applications}`);

    // 4. Test API calculations
    console.log('\n4️⃣ Testing API calculations...');
    
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // Current month applications
    const currentMonthApps = await prisma.application.count({
      where: {
        job: { companyId: 'comp_1' },
        submittedAt: { gte: currentMonth }
      }
    });
    
    // Last week applications
    const lastWeekApps = await prisma.application.count({
      where: {
        job: { companyId: 'comp_1' },
        submittedAt: { gte: oneWeekAgo }
      }
    });
    
    // Hired applications
    const hiredApps = await prisma.application.count({
      where: {
        job: { companyId: 'comp_1' },
        status: 'hired'
      }
    });
    
    // Conversion rate
    const conversionRate = comp1Applications > 0 ? 
      Math.round((hiredApps / comp1Applications) * 100 * 10) / 10 : 0;
    
    // Average score
    const avgScoreResult = await prisma.application.aggregate({
      where: {
        job: { companyId: 'comp_1' },
        score: { not: null }
      },
      _avg: { score: true }
    });
    
    const averageScore = avgScoreResult._avg.score ? 
      Math.round(avgScoreResult._avg.score) : 0;
    
    console.log('\n   📊 API Results Preview:');
    console.log(`   - Total Applications (current month): ${currentMonthApps}`);
    console.log(`   - New Applications (last 7 days): ${lastWeekApps}`);
    console.log(`   - Conversion Rate: ${conversionRate}% (${hiredApps}/${comp1Applications})`);
    console.log(`   - Average Score: ${averageScore}`);

    // 5. Show sample data if available
    if (comp1Applications > 0) {
      console.log('\n5️⃣ Sample applications:');
      
      const sampleApps = await prisma.application.findMany({
        where: {
          job: { companyId: 'comp_1' }
        },
        take: 3,
        include: {
          candidate: {
            select: {
              firstName: true,
              lastName: true
            }
          },
          job: {
            select: {
              title: true
            }
          }
        }
      });
      
      sampleApps.forEach((app, index) => {
        console.log(`   ${index + 1}. ${app.candidate.firstName} ${app.candidate.lastName} -> ${app.job.title} (${app.status})`);
      });
    }

    // 6. Final status
    console.log('\n🎯 FINAL STATUS:');
    if (comp1Applications > 0) {
      console.log('✅ SUCCESS: API should return real data!');
      console.log('✅ Application Management page should show actual statistics');
    } else {
      console.log('⚠️ No applications found for comp_1');
      console.log('💡 The page will show mock data as fallback');
      console.log('💡 Import real data or create test applications to see real statistics');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyAndFixData();
